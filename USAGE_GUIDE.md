# Git Batch Pusher - Complete Usage Guide

## Step-by-Step Setup for Your Project

### Step 1: Prepare Your Environment

1. **Navigate to your target Git repository directory**:
   ```bash
   cd /path/to/your/git/repository
   ```

2. **If you don't have a Git repository yet, create one**:
   ```bash
   git init
   git remote add origin https://github.com/nam27062002/RO.git
   ```

3. **Copy the Git Batch Pusher files to your repository directory**:
   - `git_batch_pusher.py`
   - `config.json` (or create one)

### Step 2: Configure the Tool

**Option A: Use the interactive setup (Recommended)**
```bash
python setup.py
```

**Option B: Create configuration manually**
```bash
python git_batch_pusher.py --create-config
```

Then edit `config.json` with your specific settings:
```json
{
  "source_path": "E:\\RO",
  "git_repo_url": "https://github.com/nam27062002/RO.git",
  "batch_size_mb": 1024,
  "commit_message_template": "Batch commit {batch_num}: {file_count} files ({size_mb:.1f}MB)",
  "exclude_patterns": [
    ".git", "__pycache__", "*.pyc", "*.pyo", 
    ".DS_Store", "Thumbs.db", "*.tmp", "*.log",
    "node_modules", ".vscode", ".idea", "*.exe", "*.dll"
  ]
}
```

### Step 3: Test the Configuration (Dry Run)

Before actually pushing files, test your configuration:
```bash
python git_batch_pusher.py --config config.json --dry-run
```

This will show you:
- How many files will be processed
- How many batches will be created
- The size of each batch
- Which files would be included/excluded

### Step 4: Run the Tool

**Start the batch push process**:
```bash
python git_batch_pusher.py --config config.json
```

**Monitor the progress**:
- The tool will show detailed progress for each batch
- All operations are logged to `git_batch_pusher.log`
- Progress is automatically saved for resume functionality

### Step 5: Handle Interruptions (If Needed)

If the process is interrupted for any reason:
```bash
python git_batch_pusher.py --config config.json --resume
```

## Configuration Parameters Explained

### Essential Settings

- **`source_path`**: The full path to your project directory (e.g., "E:\\RO")
- **`git_repo_url`**: Your Git repository URL (e.g., "https://github.com/nam27062002/RO.git")
- **`batch_size_mb`**: Maximum size per batch in MB (default: 1024 = 1GB)

### Advanced Settings

- **`commit_message_template`**: Template for commit messages
  - `{batch_num}`: Current batch number
  - `{file_count}`: Number of files in batch
  - `{size_mb}`: Total size of batch in MB

- **`exclude_patterns`**: File patterns to skip
  - Supports wildcards (*.pyc, *.tmp)
  - Directory names (.git, __pycache__)
  - Add your own patterns as needed

## Common Scenarios

### Scenario 1: Large Project with Mixed File Types
```json
{
  "source_path": "E:\\RO",
  "git_repo_url": "https://github.com/nam27062002/RO.git",
  "batch_size_mb": 512,
  "exclude_patterns": [
    ".git", "*.exe", "*.dll", "*.so", "*.dylib",
    "__pycache__", "node_modules", ".vscode",
    "*.log", "*.tmp", "*.cache"
  ]
}
```

### Scenario 2: Code-Only Project
```json
{
  "source_path": "E:\\RO",
  "git_repo_url": "https://github.com/nam27062002/RO.git",
  "batch_size_mb": 100,
  "exclude_patterns": [
    ".git", "*.pyc", "*.pyo", "__pycache__",
    "node_modules", ".vscode", ".idea",
    "*.log", "*.tmp", "build", "dist"
  ]
}
```

### Scenario 3: Media-Heavy Project
```json
{
  "source_path": "E:\\RO",
  "git_repo_url": "https://github.com/nam27062002/RO.git",
  "batch_size_mb": 2048,
  "exclude_patterns": [
    ".git", "*.tmp", "*.cache",
    "Thumbs.db", ".DS_Store"
  ]
}
```

## Troubleshooting

### Issue: "Git push failed"
**Solutions**:
1. Check your internet connection
2. Verify Git credentials: `git config --list`
3. Test manual push: `git push origin main`
4. Check repository permissions

### Issue: "Source path does not exist"
**Solutions**:
1. Verify the path in config.json
2. Use absolute paths (e.g., "E:\\RO" not "E:/RO")
3. Check for typos in the path

### Issue: "Current directory is not a Git repository"
**Solutions**:
1. Run `git init` in your target directory
2. Add remote: `git remote add origin <your-repo-url>`
3. Ensure you're in the correct directory

### Issue: Batch size too large
**Solutions**:
1. Reduce `batch_size_mb` in config.json
2. Try 512MB or 256MB for repositories with strict limits
3. Check your Git hosting service's file size limits

## Performance Tips

1. **Optimal Batch Size**: Start with 1GB, adjust based on your network and repository limits
2. **Exclude Unnecessary Files**: Use comprehensive exclude patterns to skip build artifacts, logs, etc.
3. **Network Considerations**: Smaller batches for slower connections
4. **Resume Capability**: Don't worry about interruptions - the tool can resume

## Security Notes

1. **Credentials**: Ensure your Git credentials are properly configured
2. **Sensitive Files**: Add sensitive files to exclude patterns
3. **Repository Access**: Verify you have push permissions to the target repository

## Getting Help

1. **View all options**: `python git_batch_pusher.py --help`
2. **Test configuration**: Use `--dry-run` flag
3. **Check logs**: Review `git_batch_pusher.log` for detailed information
4. **Resume interrupted process**: Use `--resume` flag
