#!/usr/bin/env python3
"""
Git Batch Pusher GUI - Graphical interface for the Git Batch Pusher tool.

This GUI application provides an easy-to-use interface for pushing large projects
to Git repositories in manageable chunks.

Author: Generated for project migration assistance
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import queue
import os
import json
import subprocess
from pathlib import Path
from datetime import datetime
import sys

# Import the core functionality from the command-line version
from git_batch_pusher import Config, GitBatchPusher


class ToolTip:
    """Simple tooltip implementation for tkinter widgets."""
    
    def __init__(self, widget, text):
        self.widget = widget
        self.text = text
        self.tooltip = None
        self.widget.bind("<Enter>", self.on_enter)
        self.widget.bind("<Leave>", self.on_leave)
    
    def on_enter(self, event=None):
        try:
            # Get widget position
            x = self.widget.winfo_rootx() + 25
            y = self.widget.winfo_rooty() + 25

            self.tooltip = tk.Toplevel(self.widget)
            self.tooltip.wm_overrideredirect(True)
            self.tooltip.wm_geometry(f"+{x}+{y}")

            label = tk.Label(self.tooltip, text=self.text, background="lightyellow",
                            relief="solid", borderwidth=1, font=("Arial", 9))
            label.pack()
        except:
            # Silently fail if tooltip creation fails
            pass
    
    def on_leave(self, event=None):
        if self.tooltip:
            self.tooltip.destroy()
            self.tooltip = None


class GitBatchPusherGUI:
    """Main GUI application class."""
    
    def __init__(self, root):
        self.root = root
        self.root.title("Git Batch Pusher - GUI")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)
        
        # Initialize variables
        self.config = None
        self.pusher = None
        self.processing_thread = None
        self.is_processing = False
        self.log_queue = queue.Queue()
        
        # Create GUI elements
        self.create_widgets()
        self.setup_layout()
        self.load_last_config()
        
        # Start log queue processing
        self.process_log_queue()
    
    def create_widgets(self):
        """Create all GUI widgets."""
        # Main notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        
        # Configuration tab
        self.config_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.config_frame, text="Configuration")
        
        # Progress tab
        self.progress_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.progress_frame, text="Progress")
        
        self.create_config_widgets()
        self.create_progress_widgets()
        self.create_status_bar()
    
    def create_config_widgets(self):
        """Create configuration tab widgets."""
        # Source path section
        source_frame = ttk.LabelFrame(self.config_frame, text="Source Configuration", padding=10)
        
        ttk.Label(source_frame, text="Source Path:").grid(row=0, column=0, sticky="w", pady=2)
        self.source_path_var = tk.StringVar()
        self.source_path_entry = ttk.Entry(source_frame, textvariable=self.source_path_var, width=50)
        self.source_path_entry.grid(row=0, column=1, padx=5, pady=2, sticky="ew")
        
        self.browse_button = ttk.Button(source_frame, text="Browse", command=self.browse_source_path)
        self.browse_button.grid(row=0, column=2, padx=5, pady=2)
        
        # Git repository section
        git_frame = ttk.LabelFrame(self.config_frame, text="Git Repository", padding=10)
        
        ttk.Label(git_frame, text="Repository URL:").grid(row=0, column=0, sticky="w", pady=2)
        self.git_url_var = tk.StringVar()
        self.git_url_entry = ttk.Entry(git_frame, textvariable=self.git_url_var, width=60)
        self.git_url_entry.grid(row=0, column=1, padx=5, pady=2, sticky="ew")
        
        # Batch settings section
        batch_frame = ttk.LabelFrame(self.config_frame, text="Batch Settings", padding=10)
        
        ttk.Label(batch_frame, text="Batch Size (MB):").grid(row=0, column=0, sticky="w", pady=2)
        self.batch_size_var = tk.StringVar(value="1024")
        self.batch_size_entry = ttk.Entry(batch_frame, textvariable=self.batch_size_var, width=10)
        self.batch_size_entry.grid(row=0, column=1, padx=5, pady=2, sticky="w")
        
        ttk.Label(batch_frame, text="Commit Message Template:").grid(row=1, column=0, sticky="w", pady=2)
        self.commit_template_var = tk.StringVar(value="Batch commit {batch_num}: {file_count} files ({size_mb:.1f}MB)")
        self.commit_template_entry = ttk.Entry(batch_frame, textvariable=self.commit_template_var, width=60)
        self.commit_template_entry.grid(row=1, column=1, padx=5, pady=2, sticky="ew")
        
        # Exclude patterns section
        exclude_frame = ttk.LabelFrame(self.config_frame, text="Exclude Patterns", padding=10)
        
        ttk.Label(exclude_frame, text="Patterns (one per line):").grid(row=0, column=0, sticky="nw", pady=2)
        self.exclude_text = scrolledtext.ScrolledText(exclude_frame, width=50, height=8)
        self.exclude_text.grid(row=0, column=1, padx=5, pady=2, sticky="ew")
        
        # Default exclude patterns
        default_patterns = [".git", "__pycache__", "*.pyc", "*.pyo", ".DS_Store", 
                          "Thumbs.db", "*.tmp", "*.log", "node_modules", ".vscode", ".idea"]
        self.exclude_text.insert("1.0", "\n".join(default_patterns))
        
        # Action buttons
        button_frame = ttk.Frame(self.config_frame)
        
        self.validate_button = ttk.Button(button_frame, text="Validate Config", command=self.validate_config)
        self.validate_button.pack(side="left", padx=5, pady=5)
        
        self.test_button = ttk.Button(button_frame, text="Test (Dry Run)", command=self.test_configuration)
        self.test_button.pack(side="left", padx=5, pady=5)
        
        self.save_config_button = ttk.Button(button_frame, text="Save Config", command=self.save_config)
        self.save_config_button.pack(side="left", padx=5, pady=5)
        
        self.load_config_button = ttk.Button(button_frame, text="Load Config", command=self.load_config)
        self.load_config_button.pack(side="left", padx=5, pady=5)
        
        # Pack frames
        source_frame.pack(fill="x", padx=10, pady=5)
        git_frame.pack(fill="x", padx=10, pady=5)
        batch_frame.pack(fill="x", padx=10, pady=5)
        exclude_frame.pack(fill="both", expand=True, padx=10, pady=5)
        button_frame.pack(fill="x", padx=10, pady=5)
        
        # Configure grid weights
        source_frame.columnconfigure(1, weight=1)
        git_frame.columnconfigure(1, weight=1)
        batch_frame.columnconfigure(1, weight=1)
        exclude_frame.columnconfigure(1, weight=1)
        
        # Add tooltips
        ToolTip(self.source_path_entry, "Path to the source project directory (e.g., E:\\RO)")
        ToolTip(self.git_url_entry, "Git repository URL (e.g., https://github.com/user/repo.git)")
        ToolTip(self.batch_size_entry, "Maximum size per batch in MB (default: 1024)")
        ToolTip(self.exclude_text, "File patterns to exclude from processing (supports wildcards)")
    
    def create_progress_widgets(self):
        """Create progress tab widgets."""
        # Control buttons
        control_frame = ttk.Frame(self.progress_frame)
        
        self.start_button = ttk.Button(control_frame, text="Start Push", command=self.start_processing)
        self.start_button.pack(side="left", padx=5, pady=5)
        
        self.resume_button = ttk.Button(control_frame, text="Resume", command=self.resume_processing)
        self.resume_button.pack(side="left", padx=5, pady=5)
        
        self.cancel_button = ttk.Button(control_frame, text="Cancel", command=self.cancel_processing, state="disabled")
        self.cancel_button.pack(side="left", padx=5, pady=5)
        
        # Progress information
        progress_info_frame = ttk.LabelFrame(self.progress_frame, text="Progress Information", padding=10)
        
        # Overall progress
        ttk.Label(progress_info_frame, text="Overall Progress:").grid(row=0, column=0, sticky="w", pady=2)
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_info_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=0, column=1, padx=5, pady=2, sticky="ew")
        
        self.progress_label = ttk.Label(progress_info_frame, text="Ready")
        self.progress_label.grid(row=0, column=2, padx=5, pady=2)
        
        # Current batch info
        ttk.Label(progress_info_frame, text="Current Batch:").grid(row=1, column=0, sticky="w", pady=2)
        self.batch_info_label = ttk.Label(progress_info_frame, text="Not started")
        self.batch_info_label.grid(row=1, column=1, columnspan=2, padx=5, pady=2, sticky="w")
        
        # Log output
        log_frame = ttk.LabelFrame(self.progress_frame, text="Log Output", padding=10)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, width=80, height=15, state="disabled")
        self.log_text.pack(fill="both", expand=True)
        
        # Current batch files
        files_frame = ttk.LabelFrame(self.progress_frame, text="Current Batch Files", padding=10)
        
        # Create treeview for files
        self.files_tree = ttk.Treeview(files_frame, columns=("Size",), height=8)
        self.files_tree.heading("#0", text="File Path")
        self.files_tree.heading("Size", text="Size (KB)")
        self.files_tree.column("Size", width=100)
        
        files_scrollbar = ttk.Scrollbar(files_frame, orient="vertical", command=self.files_tree.yview)
        self.files_tree.configure(yscrollcommand=files_scrollbar.set)
        
        self.files_tree.pack(side="left", fill="both", expand=True)
        files_scrollbar.pack(side="right", fill="y")
        
        # Pack frames
        control_frame.pack(fill="x", padx=10, pady=5)
        progress_info_frame.pack(fill="x", padx=10, pady=5)
        log_frame.pack(fill="both", expand=True, padx=10, pady=5)
        files_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # Configure grid weights
        progress_info_frame.columnconfigure(1, weight=1)

    def create_status_bar(self):
        """Create status bar at the bottom."""
        self.status_frame = ttk.Frame(self.root)

        self.status_var = tk.StringVar(value="Ready")
        self.status_label = ttk.Label(self.status_frame, textvariable=self.status_var, relief="sunken")
        self.status_label.pack(side="left", fill="x", expand=True, padx=2, pady=2)

        # Validation indicators
        self.source_status_var = tk.StringVar(value="❓ Source")
        self.source_status_label = ttk.Label(self.status_frame, textvariable=self.source_status_var)
        self.source_status_label.pack(side="right", padx=5)

        self.git_status_var = tk.StringVar(value="❓ Git")
        self.git_status_label = ttk.Label(self.status_frame, textvariable=self.git_status_var)
        self.git_status_label.pack(side="right", padx=5)

    def setup_layout(self):
        """Setup the main layout."""
        self.notebook.pack(fill="both", expand=True, padx=5, pady=5)
        self.status_frame.pack(fill="x", side="bottom")

    def browse_source_path(self):
        """Open file dialog to select source directory."""
        directory = filedialog.askdirectory(
            title="Select Source Project Directory",
            initialdir=self.source_path_var.get() or os.path.expanduser("~")
        )
        if directory:
            self.source_path_var.set(directory)
            self.validate_source_path()

    def validate_source_path(self):
        """Validate the source path."""
        path = self.source_path_var.get()
        if not path:
            self.source_status_var.set("❓ Source")
            return False

        if os.path.exists(path) and os.path.isdir(path):
            self.source_status_var.set("✅ Source")
            return True
        else:
            self.source_status_var.set("❌ Source")
            return False

    def validate_git_repo(self):
        """Validate Git repository setup."""
        if not os.path.exists('.git'):
            self.git_status_var.set("❌ Git")
            return False

        try:
            result = subprocess.run(['git', 'status'], capture_output=True, text=True, check=True)
            self.git_status_var.set("✅ Git")
            return True
        except subprocess.CalledProcessError:
            self.git_status_var.set("❌ Git")
            return False

    def get_exclude_patterns(self):
        """Get exclude patterns from text widget."""
        patterns_text = self.exclude_text.get("1.0", "end-1c")
        patterns = [line.strip() for line in patterns_text.split("\n") if line.strip()]
        return patterns

    def create_config_from_gui(self):
        """Create Config object from GUI inputs."""
        try:
            batch_size = int(self.batch_size_var.get())
        except ValueError:
            batch_size = 1024

        return Config(
            source_path=self.source_path_var.get(),
            git_repo_url=self.git_url_var.get(),
            batch_size_mb=batch_size,
            commit_message_template=self.commit_template_var.get(),
            exclude_patterns=self.get_exclude_patterns()
        )

    def validate_config(self):
        """Validate the current configuration."""
        self.log_message("Validating configuration...")

        # Validate source path
        if not self.validate_source_path():
            messagebox.showerror("Validation Error", "Source path is invalid or does not exist.")
            return False

        # Validate Git repository
        if not self.validate_git_repo():
            messagebox.showerror("Validation Error",
                               "Current directory is not a Git repository or Git is not accessible.")
            return False

        # Validate Git URL
        git_url = self.git_url_var.get()
        if not git_url:
            messagebox.showerror("Validation Error", "Git repository URL is required.")
            return False

        # Validate batch size
        try:
            batch_size = int(self.batch_size_var.get())
            if batch_size <= 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("Validation Error", "Batch size must be a positive integer.")
            return False

        self.log_message("✅ Configuration validation passed!")
        messagebox.showinfo("Validation Success", "Configuration is valid!")
        return True

    def test_configuration(self):
        """Run a dry run test of the configuration."""
        if not self.validate_config():
            return

        self.log_message("Running dry run test...")

        try:
            config = self.create_config_from_gui()
            pusher = GitBatchPusher(config)

            # Redirect pusher logs to GUI
            self.setup_pusher_logging(pusher)

            files = pusher.scan_files()
            if not files:
                self.log_message("⚠️ No files found to process")
                messagebox.showwarning("Test Result", "No files found to process with current configuration.")
                return

            batches = pusher.create_batches(files)

            self.log_message(f"📊 Test Results:")
            self.log_message(f"   Total files: {len(files)}")
            self.log_message(f"   Total size: {sum(size for _, size in files) / (1024*1024):.1f} MB")
            self.log_message(f"   Number of batches: {len(batches)}")

            for i, batch in enumerate(batches[:5], 1):  # Show first 5 batches
                size_mb = sum(size for _, size in batch) / (1024 * 1024)
                self.log_message(f"   Batch {i}: {len(batch)} files ({size_mb:.1f} MB)")

            if len(batches) > 5:
                self.log_message(f"   ... and {len(batches) - 5} more batches")

            messagebox.showinfo("Test Complete",
                               f"Dry run completed successfully!\n\n"
                               f"Files to process: {len(files)}\n"
                               f"Batches to create: {len(batches)}\n"
                               f"Total size: {sum(size for _, size in files) / (1024*1024):.1f} MB")

        except Exception as e:
            self.log_message(f"❌ Test failed: {str(e)}")
            messagebox.showerror("Test Failed", f"Dry run test failed:\n{str(e)}")

    def setup_pusher_logging(self, pusher):
        """Setup logging to redirect pusher output to GUI."""
        import logging

        # Create a custom handler that sends logs to the GUI queue
        class GUILogHandler(logging.Handler):
            def __init__(self, log_queue):
                super().__init__()
                self.log_queue = log_queue

            def emit(self, record):
                log_entry = self.format(record)
                self.log_queue.put(('log', log_entry))

        # Add GUI handler to pusher's logger
        gui_handler = GUILogHandler(self.log_queue)
        gui_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        pusher.logger.addHandler(gui_handler)

    def save_config(self):
        """Save current configuration to file."""
        filename = filedialog.asksaveasfilename(
            title="Save Configuration",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            initialfilename="config.json"
        )

        if filename:
            try:
                config = self.create_config_from_gui()
                config_dict = {
                    'source_path': config.source_path,
                    'git_repo_url': config.git_repo_url,
                    'batch_size_mb': config.batch_size_mb,
                    'commit_message_template': config.commit_message_template,
                    'exclude_patterns': config.exclude_patterns,
                    'resume_file': config.resume_file
                }

                with open(filename, 'w') as f:
                    json.dump(config_dict, f, indent=2)

                self.log_message(f"✅ Configuration saved to {filename}")
                messagebox.showinfo("Save Successful", f"Configuration saved to:\n{filename}")

                # Save as last config
                self.save_last_config()

            except Exception as e:
                self.log_message(f"❌ Failed to save configuration: {str(e)}")
                messagebox.showerror("Save Failed", f"Failed to save configuration:\n{str(e)}")

    def load_config(self):
        """Load configuration from file."""
        filename = filedialog.askopenfilename(
            title="Load Configuration",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            initialdir="."
        )

        if filename:
            try:
                with open(filename, 'r') as f:
                    config_dict = json.load(f)

                # Update GUI fields
                self.source_path_var.set(config_dict.get('source_path', ''))
                self.git_url_var.set(config_dict.get('git_repo_url', ''))
                self.batch_size_var.set(str(config_dict.get('batch_size_mb', 1024)))
                self.commit_template_var.set(config_dict.get('commit_message_template',
                    'Batch commit {batch_num}: {file_count} files ({size_mb:.1f}MB)'))

                # Update exclude patterns
                patterns = config_dict.get('exclude_patterns', [])
                self.exclude_text.delete("1.0", "end")
                self.exclude_text.insert("1.0", "\n".join(patterns))

                self.log_message(f"✅ Configuration loaded from {filename}")
                messagebox.showinfo("Load Successful", f"Configuration loaded from:\n{filename}")

                # Validate after loading
                self.validate_source_path()
                self.validate_git_repo()

                # Save as last config
                self.save_last_config()

            except Exception as e:
                self.log_message(f"❌ Failed to load configuration: {str(e)}")
                messagebox.showerror("Load Failed", f"Failed to load configuration:\n{str(e)}")

    def save_last_config(self):
        """Save current configuration as last used."""
        try:
            config = self.create_config_from_gui()
            config_dict = {
                'source_path': config.source_path,
                'git_repo_url': config.git_repo_url,
                'batch_size_mb': config.batch_size_mb,
                'commit_message_template': config.commit_message_template,
                'exclude_patterns': config.exclude_patterns
            }

            with open('.last_config.json', 'w') as f:
                json.dump(config_dict, f, indent=2)
        except:
            pass  # Silently fail for last config

    def load_last_config(self):
        """Load last used configuration."""
        try:
            if os.path.exists('.last_config.json'):
                with open('.last_config.json', 'r') as f:
                    config_dict = json.load(f)

                self.source_path_var.set(config_dict.get('source_path', ''))
                self.git_url_var.set(config_dict.get('git_repo_url', ''))
                self.batch_size_var.set(str(config_dict.get('batch_size_mb', 1024)))
                self.commit_template_var.set(config_dict.get('commit_message_template',
                    'Batch commit {batch_num}: {file_count} files ({size_mb:.1f}MB)'))

                patterns = config_dict.get('exclude_patterns', [])
                if patterns:
                    self.exclude_text.delete("1.0", "end")
                    self.exclude_text.insert("1.0", "\n".join(patterns))

                # Validate after loading
                self.validate_source_path()
                self.validate_git_repo()
        except:
            pass  # Silently fail for last config

    def start_processing(self):
        """Start the batch processing."""
        if not self.validate_config():
            return

        if self.is_processing:
            messagebox.showwarning("Already Processing", "A batch process is already running.")
            return

        # Confirm start
        result = messagebox.askyesno("Confirm Start",
                                   "Are you sure you want to start pushing files to the Git repository?\n\n"
                                   "This will begin uploading files and cannot be easily undone.")
        if not result:
            return

        self.log_message("🚀 Starting batch processing...")
        self.start_processing_thread(resume=False)

    def resume_processing(self):
        """Resume interrupted processing."""
        if not self.validate_config():
            return

        if self.is_processing:
            messagebox.showwarning("Already Processing", "A batch process is already running.")
            return

        # Check if resume file exists
        config = self.create_config_from_gui()
        if not os.path.exists(config.resume_file):
            messagebox.showinfo("No Resume Data", "No previous session found to resume.")
            return

        result = messagebox.askyesno("Confirm Resume",
                                   "Resume the previous interrupted batch process?")
        if not result:
            return

        self.log_message("🔄 Resuming batch processing...")
        self.start_processing_thread(resume=True)

    def start_processing_thread(self, resume=False):
        """Start processing in a separate thread."""
        self.is_processing = True
        self.update_button_states()

        # Switch to progress tab
        self.notebook.select(self.progress_frame)

        # Start processing thread
        self.processing_thread = threading.Thread(
            target=self.process_batches_worker,
            args=(resume,),
            daemon=True
        )
        self.processing_thread.start()

    def process_batches_worker(self, resume=False):
        """Worker method for batch processing (runs in separate thread)."""
        try:
            config = self.create_config_from_gui()
            self.pusher = GitBatchPusher(config)

            # Setup logging
            self.setup_pusher_logging(self.pusher)

            # Custom pusher class to send progress updates
            class GUIGitBatchPusher(GitBatchPusher):
                def __init__(self, config, log_queue):
                    super().__init__(config)
                    self.gui_log_queue = log_queue

                def process_batches(self, resume=False):
                    files = self.scan_files()
                    if not files:
                        self.gui_log_queue.put(('error', "No files found to process"))
                        return False

                    batches = self.create_batches(files)
                    start_batch = 1
                    processed_files = []

                    # Handle resume functionality
                    if resume:
                        progress = self.load_progress()
                        if progress:
                            start_batch = progress['batch_num']
                            processed_files = progress['processed_files']

                    total_batches = len(batches)

                    # Process each batch
                    for i, batch in enumerate(batches[start_batch-1:], start=start_batch):
                        # Update progress
                        progress_percent = ((i - 1) / total_batches) * 100
                        self.gui_log_queue.put(('progress', progress_percent))

                        batch_size_mb = sum(size for _, size in batch) / (1024 * 1024)

                        # Update batch info
                        batch_info = f"Batch {i}/{total_batches} - {len(batch)} files ({batch_size_mb:.1f}MB)"
                        self.gui_log_queue.put(('batch_info', batch_info))

                        # Update file list
                        self.gui_log_queue.put(('files', batch))

                        # Copy files to repository
                        if not self.copy_files_to_repo(batch):
                            self.gui_log_queue.put(('error', f"Failed to copy files for batch {i}"))
                            return False

                        # Git operations
                        if not self.git_add_commit_push(batch, i):
                            self.gui_log_queue.put(('error', f"Failed to push batch {i}"))
                            return False

                        # Update progress
                        batch_files = [filepath for filepath, _ in batch]
                        processed_files.extend(batch_files)
                        self.save_progress(i + 1, processed_files)

                    # Final progress update
                    self.gui_log_queue.put(('progress', 100))
                    self.gui_log_queue.put(('batch_info', f"Completed! {total_batches} batches processed"))

                    # Clean up progress file
                    if os.path.exists(self.config.resume_file):
                        os.remove(self.config.resume_file)

                    self.gui_log_queue.put(('complete', "All batches processed successfully!"))
                    return True

            # Use custom pusher
            gui_pusher = GUIGitBatchPusher(config, self.log_queue)
            success = gui_pusher.process_batches(resume=resume)

            if not success:
                self.log_queue.put(('error', "Processing failed"))

        except Exception as e:
            self.log_queue.put(('error', f"Processing error: {str(e)}"))
        finally:
            self.log_queue.put(('finished', None))

    def cancel_processing(self):
        """Cancel the current processing."""
        if not self.is_processing:
            return

        result = messagebox.askyesno("Confirm Cancel",
                                   "Are you sure you want to cancel the current process?\n\n"
                                   "You can resume later using the Resume button.")
        if result:
            self.log_message("🛑 Processing cancelled by user")
            self.is_processing = False
            self.update_button_states()

    def update_button_states(self):
        """Update button states based on processing status."""
        if self.is_processing:
            self.start_button.config(state="disabled")
            self.resume_button.config(state="disabled")
            self.cancel_button.config(state="normal")
            self.status_var.set("Processing...")
        else:
            self.start_button.config(state="normal")
            self.resume_button.config(state="normal")
            self.cancel_button.config(state="disabled")
            self.status_var.set("Ready")

    def log_message(self, message):
        """Add a message to the log."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"

        self.log_text.config(state="normal")
        self.log_text.insert("end", formatted_message + "\n")
        self.log_text.see("end")
        self.log_text.config(state="disabled")

    def update_files_display(self, batch):
        """Update the files display with current batch files."""
        # Clear existing items
        for item in self.files_tree.get_children():
            self.files_tree.delete(item)

        # Add new files
        for filepath, size in batch:
            size_kb = size / 1024
            self.files_tree.insert("", "end", text=filepath, values=(f"{size_kb:.1f}",))

    def process_log_queue(self):
        """Process messages from the log queue."""
        try:
            while True:
                message_type, data = self.log_queue.get_nowait()

                if message_type == 'log':
                    self.log_message(data)
                elif message_type == 'progress':
                    self.progress_var.set(data)
                    self.progress_label.config(text=f"{data:.1f}%")
                elif message_type == 'batch_info':
                    self.batch_info_label.config(text=data)
                elif message_type == 'files':
                    self.update_files_display(data)
                elif message_type == 'error':
                    self.log_message(f"❌ {data}")
                    messagebox.showerror("Processing Error", data)
                    self.is_processing = False
                    self.update_button_states()
                elif message_type == 'complete':
                    self.log_message(f"✅ {data}")
                    messagebox.showinfo("Processing Complete", data)
                    self.is_processing = False
                    self.update_button_states()
                elif message_type == 'finished':
                    self.is_processing = False
                    self.update_button_states()

        except queue.Empty:
            pass

        # Schedule next check
        self.root.after(100, self.process_log_queue)


def main():
    """Main entry point for the GUI application."""
    # Check if tkinter is available
    try:
        import tkinter as tk
    except ImportError:
        print("Error: tkinter is not available. Please install tkinter.")
        sys.exit(1)

    # Create and run the application
    root = tk.Tk()
    app = GitBatchPusherGUI(root)

    # Handle window closing
    def on_closing():
        if app.is_processing:
            result = messagebox.askyesno("Confirm Exit",
                                       "A process is currently running. Are you sure you want to exit?\n\n"
                                       "The process will be interrupted and can be resumed later.")
            if not result:
                return

        app.save_last_config()
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)

    # Start the GUI
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        sys.exit(0)


if __name__ == "__main__":
    main()
