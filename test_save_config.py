#!/usr/bin/env python3
"""
Test script specifically for the save config functionality
"""

import tkinter as tk
from tkinter import filedialog
import tempfile
import os
import json

def test_save_config_dialog():
    """Test the save config file dialog functionality."""
    print("Testing save config file dialog...")
    
    try:
        root = tk.Tk()
        root.withdraw()  # Hide the main window
        
        # Test the file dialog parameters that were causing the error
        print("Creating file dialog with correct parameters...")
        
        # This should work without errors now
        test_filename = filedialog.asksaveasfilename(
            title="Test Save Configuration",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            initialfile="config.json"
        )
        
        print("✅ File dialog created successfully (user cancelled, which is expected)")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ File dialog test failed: {e}")
        return False

def test_config_creation():
    """Test creating and saving a configuration."""
    print("Testing configuration creation and saving...")
    
    try:
        from git_batch_pusher_gui import GitBatchPusherGUI
        
        root = tk.Tk()
        root.withdraw()
        
        app = GitBatchPusherGUI(root)
        
        # Set test configuration
        app.source_path_var.set("E:\\RO")
        app.git_url_var.set("https://github.com/nam27062002/RO.git")
        app.batch_size_var.set("1024")
        app.commit_template_var.set("Test commit {batch_num}: {file_count} files")
        
        # Set exclude patterns
        app.exclude_text.delete("1.0", "end")
        app.exclude_text.insert("1.0", ".git\n*.pyc\n*.tmp")
        
        # Create config object
        config = app.create_config_from_gui()
        
        # Verify config
        assert config.source_path == "E:\\RO"
        assert config.git_repo_url == "https://github.com/nam27062002/RO.git"
        assert config.batch_size_mb == 1024
        assert ".git" in config.exclude_patterns
        assert "*.pyc" in config.exclude_patterns
        
        print("✅ Configuration creation works correctly")
        
        # Test saving to a temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_filename = f.name
        
        try:
            config_dict = {
                'source_path': config.source_path,
                'git_repo_url': config.git_repo_url,
                'batch_size_mb': config.batch_size_mb,
                'commit_message_template': config.commit_message_template,
                'exclude_patterns': config.exclude_patterns,
                'resume_file': config.resume_file
            }
            
            with open(temp_filename, 'w') as f:
                json.dump(config_dict, f, indent=2)
            
            print("✅ Configuration saved to temporary file successfully")
            
            # Test loading it back
            with open(temp_filename, 'r') as f:
                loaded_config = json.load(f)
            
            assert loaded_config['source_path'] == "E:\\RO"
            assert loaded_config['git_repo_url'] == "https://github.com/nam27062002/RO.git"
            assert loaded_config['batch_size_mb'] == 1024
            
            print("✅ Configuration loaded back successfully")
            
        finally:
            # Clean up
            if os.path.exists(temp_filename):
                os.unlink(temp_filename)
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def main():
    """Run the save config tests."""
    print("Save Config Functionality Tests")
    print("=" * 35)
    
    tests_passed = 0
    total_tests = 2
    
    # Test 1: File dialog
    if test_save_config_dialog():
        tests_passed += 1
    
    # Test 2: Config creation and saving
    if test_config_creation():
        tests_passed += 1
    
    print(f"\n" + "=" * 35)
    print(f"Tests completed: {tests_passed}/{total_tests} passed")
    
    if tests_passed == total_tests:
        print("🎉 All save config tests passed!")
        print("\nThe save config functionality is now working correctly.")
        print("You can use the GUI without the previous error.")
        return True
    else:
        print("⚠️ Some tests failed.")
        return False

if __name__ == "__main__":
    main()
