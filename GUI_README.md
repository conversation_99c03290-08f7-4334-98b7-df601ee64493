# Git Batch Pusher GUI - User Guide

A user-friendly graphical interface for the Git Batch Pusher tool that helps push large projects to Git repositories in manageable chunks.

## 🚀 Quick Start

### Windows Users
1. Double-click `launch_gui.bat` to start the application
2. Or run `python launch_gui.py` from command line

### Other Platforms
```bash
python launch_gui.py
```

## 📋 Requirements

- Python 3.7 or higher
- tkinter (usually included with Python)
- Git installed and configured
- All files from the Git Batch Pusher package

## 🖥️ Interface Overview

The GUI is organized into two main tabs:

### Configuration Tab
This is where you set up your project settings:

#### Source Configuration
- **Source Path**: The directory containing your project files
- **Browse Button**: Click to select the source directory using a file dialog

#### Git Repository
- **Repository URL**: Your Git repository URL (e.g., https://github.com/nam27062002/RO.git)

#### Batch Settings
- **Batch Size (MB)**: Maximum size per batch in megabytes (default: 1024 = 1GB)
- **Commit Message Template**: Template for commit messages with placeholders:
  - `{batch_num}`: Current batch number
  - `{file_count}`: Number of files in the batch
  - `{size_mb}`: Total size of the batch in MB

#### Exclude Patterns
- **Patterns**: List of file patterns to exclude (one per line)
- Supports wildcards (*.pyc, *.tmp)
- Directory names (.git, __pycache__)

#### Action Buttons
- **Validate Config**: Check if your configuration is valid
- **Test (Dry Run)**: Preview what will be processed without actually doing it
- **Save Config**: Save current settings to a JSON file
- **Load Config**: Load settings from a previously saved JSON file

### Progress Tab
This tab shows the processing status and controls:

#### Control Buttons
- **Start Push**: Begin the batch processing
- **Resume**: Continue from a previously interrupted session
- **Cancel**: Stop the current processing (can be resumed later)

#### Progress Information
- **Overall Progress**: Progress bar showing completion percentage
- **Current Batch**: Information about the batch being processed

#### Log Output
- Real-time log messages showing what's happening
- Timestamps for all operations
- Success/error indicators

#### Current Batch Files
- Table showing files being processed in the current batch
- File paths and sizes

## 🔧 How to Use

### Step 1: Configure Your Project

1. **Launch the application**
2. **Set Source Path**: 
   - Click "Browse" to select your project directory (E:\RO)
   - Or type the path directly
3. **Set Git Repository URL**: 
   - Enter your repository URL: https://github.com/nam27062002/RO.git
4. **Adjust Batch Size**: 
   - Default 1024 MB (1GB) is usually good
   - Reduce if you have network or repository size limitations
5. **Review Exclude Patterns**: 
   - Default patterns exclude common unwanted files
   - Add your own patterns if needed

### Step 2: Validate and Test

1. **Click "Validate Config"** to check your settings
2. **Click "Test (Dry Run)"** to see what will be processed
   - Shows number of files and batches
   - No actual changes are made
3. **Review the results** in the popup dialog

### Step 3: Start Processing

1. **Switch to the Progress tab**
2. **Click "Start Push"** to begin
3. **Monitor progress** in real-time:
   - Progress bar shows overall completion
   - Log shows detailed operations
   - File list shows current batch contents
4. **Wait for completion** or use Cancel if needed

### Step 4: Handle Interruptions (If Needed)

If the process is interrupted:
1. **Restart the application**
2. **Load your configuration**
3. **Click "Resume"** to continue from where it left off

## 💡 Tips and Best Practices

### Configuration Tips
- **Use absolute paths** for source directory to avoid confusion
- **Start with smaller batch sizes** (256-512 MB) if you're unsure
- **Test first** with dry run before actual processing
- **Save your configuration** for future use

### Processing Tips
- **Ensure stable internet connection** before starting
- **Don't close the application** during processing
- **Monitor the log** for any error messages
- **Use Resume feature** if interrupted

### Troubleshooting
- **Red status indicators**: Check the status bar for validation issues
- **Git errors**: Ensure you're in a Git repository and have push permissions
- **Source path errors**: Verify the path exists and is accessible
- **Network issues**: Check internet connection and repository access

## 🎯 Status Indicators

The status bar at the bottom shows:
- **❓ Source**: Source path not set or not validated
- **✅ Source**: Source path is valid
- **❌ Source**: Source path is invalid or doesn't exist
- **❓ Git**: Git repository not validated
- **✅ Git**: Git repository is valid and accessible
- **❌ Git**: Git repository issues (not a repo, no access, etc.)

## 📁 Configuration Files

### Automatic Saving
- The application automatically saves your last used settings
- Settings are restored when you restart the application

### Manual Save/Load
- **Save Config**: Export settings to a JSON file for sharing or backup
- **Load Config**: Import settings from a JSON file
- Configuration files are compatible with the command-line version

### Sample Configuration
```json
{
  "source_path": "E:\\RO",
  "git_repo_url": "https://github.com/nam27062002/RO.git",
  "batch_size_mb": 1024,
  "commit_message_template": "Batch commit {batch_num}: {file_count} files ({size_mb:.1f}MB)",
  "exclude_patterns": [
    ".git", "__pycache__", "*.pyc", "*.pyo", 
    ".DS_Store", "Thumbs.db", "*.tmp", "*.log"
  ]
}
```

## 🚨 Important Notes

### Before Starting
- **Backup your project** before processing
- **Ensure Git repository is properly set up** with remote origin
- **Test with a small subset** if you're unsure
- **Check repository size limits** on your Git hosting service

### During Processing
- **Don't interrupt** unless absolutely necessary
- **Monitor for errors** in the log output
- **Keep the application window open**
- **Ensure stable power and internet**

### After Completion
- **Verify the upload** on your Git hosting service
- **Check that all files are present**
- **Clean up any temporary files** if needed

## 🆘 Getting Help

If you encounter issues:
1. **Check the log output** for detailed error messages
2. **Verify your configuration** using the Validate button
3. **Try a dry run** to test without making changes
4. **Check the command-line version** documentation for additional troubleshooting
5. **Ensure all prerequisites** are met (Python, Git, network access)

The GUI provides the same powerful functionality as the command-line version but with an intuitive, user-friendly interface that makes it easy to push large projects to Git repositories safely and efficiently.
