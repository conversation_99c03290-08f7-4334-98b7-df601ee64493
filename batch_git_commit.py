#!/usr/bin/env python3
"""
Batch Git Commit Script for Large Game Development Projects

This script initializes a Git repository and commits files in batches to handle
large repositories that would otherwise fail when adding all files at once.

Features:
- Initializes Git repository if not present
- Adds files in configurable batch sizes (default 1GB)
- Creates descriptive commit messages for each batch
- Handles large files with Git LFS recommendations
- Ski<PERSON> already tracked files
- Provides progress feedback
- Robust error handling
- Configurable .gitignore for game development

Author: AI Assistant
"""

import os
import sys
import subprocess
import argparse
import logging
from pathlib import Path
from typing import List, Tuple, Set
import time
from collections import defaultdict

# Configuration
DEFAULT_BATCH_SIZE_GB = 1.0
DEFAULT_MAX_FILE_SIZE_MB = 100  # Files larger than this will be flagged for LFS
GAME_DEV_GITIGNORE_PATTERNS = [
    # Build outputs
    "*.obj", "*.o", "*.a", "*.lib", "*.dll", "*.exe", "*.pdb", "*.ilk", "*.exp",
    "*.idb", "*.suo", "*.user", "*.ncb", "*.aps", "*.plg", "*.opt", "*.clw",
    
    # Temporary files
    "*.tmp", "*.temp", "*.log", "*.cache", "*.bak", "*.swp", "*.swo",
    
    # IDE files
    ".vs/", ".vscode/", "*.vcxproj.user", "*.vcxproj.filters",
    
    # Build directories
    "bin/", "obj/", "build/", "Build/", "Binaries/", "Intermediate/",
    
    # Package manager
    "node_modules/", "packages/",
    
    # Game engine specific
    "*.uasset", "*.umap",  # Unreal Engine
    "Library/", "Temp/", "Logs/",  # Unity
    
    # Large asset files (consider for LFS)
    "*.fbx", "*.max", "*.mb", "*.ma",  # 3D models
    "*.psd", "*.tga", "*.exr", "*.hdr",  # Textures
    "*.wav", "*.mp3", "*.ogg", "*.wem",  # Audio
    "*.mp4", "*.avi", "*.mov",  # Video
    
    # Perforce specific
    ".p4config", "*.p4*",
]

class BatchGitCommitter:
    def __init__(self, batch_size_gb: float = DEFAULT_BATCH_SIZE_GB, 
                 max_file_size_mb: int = DEFAULT_MAX_FILE_SIZE_MB,
                 dry_run: bool = False):
        self.batch_size_bytes = int(batch_size_gb * 1024 * 1024 * 1024)
        self.max_file_size_bytes = max_file_size_mb * 1024 * 1024
        self.dry_run = dry_run
        self.logger = self._setup_logging()
        self.git_tracked_files: Set[str] = set()
        self.large_files: List[Tuple[str, int]] = []
        
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('batch_git_commit.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        return logging.getLogger(__name__)
    
    def _run_git_command(self, cmd: List[str], check: bool = True) -> subprocess.CompletedProcess:
        """Run a git command and return the result."""
        try:
            self.logger.debug(f"Running: git {' '.join(cmd)}")
            if self.dry_run and cmd[0] in ['add', 'commit', 'init']:
                self.logger.info(f"DRY RUN: Would execute: git {' '.join(cmd)}")
                return subprocess.CompletedProcess(cmd, 0, stdout="", stderr="")
            
            result = subprocess.run(['git'] + cmd, capture_output=True, text=True, check=check)
            return result
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Git command failed: git {' '.join(cmd)}")
            self.logger.error(f"Error: {e.stderr}")
            raise
    
    def _is_git_repo(self) -> bool:
        """Check if current directory is a git repository."""
        try:
            self._run_git_command(['rev-parse', '--git-dir'], check=True)
            return True
        except subprocess.CalledProcessError:
            return False
    
    def _init_git_repo(self) -> None:
        """Initialize git repository if it doesn't exist."""
        if not self._is_git_repo():
            self.logger.info("Initializing Git repository...")
            self._run_git_command(['init'])
            self.logger.info("Git repository initialized successfully")
        else:
            self.logger.info("Git repository already exists")
    
    def _setup_gitignore(self) -> None:
        """Create or update .gitignore with game development patterns."""
        gitignore_path = Path('.gitignore')
        
        if gitignore_path.exists():
            with open(gitignore_path, 'r') as f:
                existing_content = f.read()
        else:
            existing_content = ""
        
        # Add patterns that aren't already present
        new_patterns = []
        for pattern in GAME_DEV_GITIGNORE_PATTERNS:
            if pattern not in existing_content:
                new_patterns.append(pattern)
        
        if new_patterns:
            self.logger.info(f"Adding {len(new_patterns)} patterns to .gitignore")
            if not self.dry_run:
                with open(gitignore_path, 'a') as f:
                    if existing_content and not existing_content.endswith('\n'):
                        f.write('\n')
                    f.write('\n# Game Development Patterns\n')
                    for pattern in new_patterns:
                        f.write(f"{pattern}\n")
        else:
            self.logger.info(".gitignore already contains game development patterns")
    
    def _get_tracked_files(self) -> Set[str]:
        """Get list of files already tracked by Git."""
        try:
            result = self._run_git_command(['ls-files'], check=True)
            tracked_files = set(result.stdout.strip().split('\n')) if result.stdout.strip() else set()
            self.logger.info(f"Found {len(tracked_files)} already tracked files")
            return tracked_files
        except subprocess.CalledProcessError:
            return set()
    
    def _get_untracked_files(self) -> List[Tuple[str, int]]:
        """Get list of untracked files with their sizes."""
        self.logger.info("Scanning for untracked files...")
        
        try:
            # Get list of files that would be added (respects .gitignore)
            result = self._run_git_command(['ls-files', '--others', '--exclude-standard'], check=True)
            untracked_files = result.stdout.strip().split('\n') if result.stdout.strip() else []
        except subprocess.CalledProcessError:
            # Fallback to manual file discovery
            untracked_files = []
            for root, dirs, files in os.walk('.'):
                # Skip .git directory
                if '.git' in dirs:
                    dirs.remove('.git')
                
                for file in files:
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, '.')
                    if rel_path not in self.git_tracked_files:
                        untracked_files.append(rel_path)
        
        # Get file sizes
        files_with_sizes = []
        for file_path in untracked_files:
            try:
                if os.path.exists(file_path):
                    size = os.path.getsize(file_path)
                    files_with_sizes.append((file_path, size))
                    
                    # Track large files for LFS recommendation
                    if size > self.max_file_size_bytes:
                        self.large_files.append((file_path, size))
            except (OSError, IOError) as e:
                self.logger.warning(f"Could not get size for {file_path}: {e}")
        
        self.logger.info(f"Found {len(files_with_sizes)} untracked files")
        if self.large_files:
            self.logger.warning(f"Found {len(self.large_files)} large files that may need Git LFS")
        
        return files_with_sizes

    def _create_batches(self, files_with_sizes: List[Tuple[str, int]]) -> List[List[str]]:
        """Group files into batches based on size limit."""
        batches = []
        current_batch = []
        current_batch_size = 0

        # Sort files by size (largest first) for better packing
        files_with_sizes.sort(key=lambda x: x[1], reverse=True)

        for file_path, file_size in files_with_sizes:
            # If single file exceeds batch size, put it in its own batch
            if file_size > self.batch_size_bytes:
                if current_batch:
                    batches.append(current_batch)
                    current_batch = []
                    current_batch_size = 0
                batches.append([file_path])
                self.logger.warning(f"Large file in separate batch: {file_path} ({file_size / (1024*1024):.1f} MB)")
                continue

            # If adding this file would exceed batch size, start new batch
            if current_batch_size + file_size > self.batch_size_bytes and current_batch:
                batches.append(current_batch)
                current_batch = []
                current_batch_size = 0

            current_batch.append(file_path)
            current_batch_size += file_size

        # Add the last batch if it has files
        if current_batch:
            batches.append(current_batch)

        self.logger.info(f"Created {len(batches)} batches")
        for i, batch in enumerate(batches, 1):
            batch_size_mb = sum(os.path.getsize(f) for f in batch if os.path.exists(f)) / (1024 * 1024)
            self.logger.info(f"Batch {i}: {len(batch)} files, {batch_size_mb:.1f} MB")

        return batches

    def _commit_batch(self, batch: List[str], batch_num: int, total_batches: int) -> bool:
        """Add and commit a batch of files."""
        try:
            self.logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch)} files)")

            # Add files to staging area
            for file_path in batch:
                try:
                    self._run_git_command(['add', file_path])
                except subprocess.CalledProcessError as e:
                    self.logger.error(f"Failed to add {file_path}: {e}")
                    continue

            # Check if there are any staged changes
            try:
                result = self._run_git_command(['diff', '--cached', '--name-only'])
                if not result.stdout.strip():
                    self.logger.warning(f"No changes staged for batch {batch_num}, skipping commit")
                    return True
            except subprocess.CalledProcessError:
                pass

            # Create commit message
            batch_size_mb = sum(os.path.getsize(f) for f in batch if os.path.exists(f)) / (1024 * 1024)
            commit_msg = f"Batch commit {batch_num}/{total_batches}: {len(batch)} files ({batch_size_mb:.1f} MB)"

            # Commit the batch
            self._run_git_command(['commit', '-m', commit_msg])
            self.logger.info(f"Successfully committed batch {batch_num}")
            return True

        except subprocess.CalledProcessError as e:
            self.logger.error(f"Failed to commit batch {batch_num}: {e}")
            return False

    def _print_large_files_report(self) -> None:
        """Print report of large files that might need Git LFS."""
        if not self.large_files:
            return

        self.logger.info("\n" + "="*60)
        self.logger.info("LARGE FILES REPORT - Consider Git LFS")
        self.logger.info("="*60)

        # Group by file type
        by_extension = defaultdict(list)
        for file_path, size in self.large_files:
            ext = Path(file_path).suffix.lower()
            by_extension[ext].append((file_path, size))

        for ext, files in sorted(by_extension.items()):
            total_size = sum(size for _, size in files)
            self.logger.info(f"\n{ext or 'No extension'} files: {len(files)} files, {total_size / (1024*1024):.1f} MB total")
            for file_path, size in sorted(files, key=lambda x: x[1], reverse=True)[:5]:  # Show top 5
                self.logger.info(f"  {file_path}: {size / (1024*1024):.1f} MB")
            if len(files) > 5:
                self.logger.info(f"  ... and {len(files) - 5} more files")

        self.logger.info(f"\nTo use Git LFS for these files, run:")
        self.logger.info(f"git lfs install")
        for ext in sorted(by_extension.keys()):
            if ext:
                self.logger.info(f"git lfs track '*{ext}'")
        self.logger.info("="*60)

    def run(self) -> bool:
        """Main execution method."""
        try:
            self.logger.info("Starting batch Git commit process...")
            start_time = time.time()

            # Initialize Git repository
            self._init_git_repo()

            # Setup .gitignore
            self._setup_gitignore()

            # Get already tracked files
            self.git_tracked_files = self._get_tracked_files()

            # Get untracked files
            untracked_files = self._get_untracked_files()

            if not untracked_files:
                self.logger.info("No untracked files found. Repository is up to date.")
                return True

            # Create batches
            batches = self._create_batches(untracked_files)

            if not batches:
                self.logger.info("No files to commit.")
                return True

            # Process each batch
            successful_batches = 0
            for i, batch in enumerate(batches, 1):
                if self._commit_batch(batch, i, len(batches)):
                    successful_batches += 1
                else:
                    self.logger.error(f"Batch {i} failed, continuing with next batch...")

            # Print summary
            elapsed_time = time.time() - start_time
            self.logger.info(f"\n" + "="*60)
            self.logger.info(f"BATCH COMMIT SUMMARY")
            self.logger.info(f"="*60)
            self.logger.info(f"Total batches: {len(batches)}")
            self.logger.info(f"Successful batches: {successful_batches}")
            self.logger.info(f"Failed batches: {len(batches) - successful_batches}")
            self.logger.info(f"Total files processed: {sum(len(batch) for batch in batches)}")
            self.logger.info(f"Elapsed time: {elapsed_time:.1f} seconds")

            # Print large files report
            self._print_large_files_report()

            return successful_batches == len(batches)

        except Exception as e:
            self.logger.error(f"Unexpected error: {e}")
            return False


def main():
    parser = argparse.ArgumentParser(
        description="Batch commit files to Git repository",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python batch_git_commit.py                    # Use default 1GB batches
  python batch_git_commit.py --batch-size 0.5   # Use 500MB batches
  python batch_git_commit.py --dry-run          # Preview what would be done
  python batch_git_commit.py --max-file-size 50 # Flag files >50MB for LFS
        """
    )

    parser.add_argument(
        '--batch-size',
        type=float,
        default=DEFAULT_BATCH_SIZE_GB,
        help=f'Batch size in GB (default: {DEFAULT_BATCH_SIZE_GB})'
    )

    parser.add_argument(
        '--max-file-size',
        type=int,
        default=DEFAULT_MAX_FILE_SIZE_MB,
        help=f'Max file size in MB before flagging for LFS (default: {DEFAULT_MAX_FILE_SIZE_MB})'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be done without making changes'
    )

    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Create and run the committer
    committer = BatchGitCommitter(
        batch_size_gb=args.batch_size,
        max_file_size_mb=args.max_file_size,
        dry_run=args.dry_run
    )

    success = committer.run()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
