@echo off
REM Git Batch Pusher - Windows Batch Script
REM This script provides an easy way to run the Git Batch Pusher on Windows

echo Git Batch Pusher
echo ================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

REM Check if config file exists
if not exist "config.json" (
    echo No configuration file found.
    echo.
    choice /C YN /M "Do you want to create a sample configuration file"
    if errorlevel 2 goto :manual_config
    
    echo Creating sample configuration...
    python git_batch_pusher.py --create-config
    echo.
    echo Please edit config.json with your specific settings, then run this script again.
    pause
    exit /b 0
)

:manual_config
echo.
echo Available options:
echo 1. Run with configuration file (config.json)
echo 2. Run with command line arguments
echo 3. Dry run (preview mode)
echo 4. Resume interrupted process
echo 5. Create new configuration file
echo 6. Exit
echo.

choice /C 123456 /M "Select an option"

if errorlevel 6 goto :end
if errorlevel 5 goto :create_config
if errorlevel 4 goto :resume
if errorlevel 3 goto :dry_run
if errorlevel 2 goto :command_line
if errorlevel 1 goto :config_file

:config_file
echo Running with configuration file...
python git_batch_pusher.py --config config.json
goto :end

:command_line
echo.
set /p SOURCE_PATH="Enter source path (e.g., E:\RO): "
set /p REPO_URL="Enter Git repository URL: "
set /p BATCH_SIZE="Enter batch size in MB (default 1024): "

if "%BATCH_SIZE%"=="" set BATCH_SIZE=1024

echo Running with command line arguments...
python git_batch_pusher.py --source "%SOURCE_PATH%" --url "%REPO_URL%" --batch-size %BATCH_SIZE%
goto :end

:dry_run
echo Running dry run (preview mode)...
python git_batch_pusher.py --config config.json --dry-run
goto :end

:resume
echo Resuming interrupted process...
python git_batch_pusher.py --config config.json --resume
goto :end

:create_config
echo Creating new configuration file...
python git_batch_pusher.py --create-config
echo.
echo Configuration file created. Please edit config.json with your settings.
goto :end

:end
echo.
echo Press any key to exit...
pause >nul
