#!/usr/bin/env python3
"""
Simple launcher for the Git Batch Pusher GUI.

This script provides a simple way to launch the GUI application with
proper error handling and dependency checking.
"""

import sys
import os

def check_dependencies():
    """Check if all required dependencies are available."""
    missing_deps = []
    
    # Check Python version
    if sys.version_info < (3, 7):
        print(f"Error: Python 3.7+ is required. Current version: {sys.version}")
        return False
    
    # Check tkinter
    try:
        import tkinter
    except ImportError:
        missing_deps.append("tkinter")
    
    # Check if git_batch_pusher module is available
    try:
        import git_batch_pusher
    except ImportError:
        missing_deps.append("git_batch_pusher.py (make sure it's in the same directory)")
    
    if missing_deps:
        print("Error: Missing dependencies:")
        for dep in missing_deps:
            print(f"  - {dep}")
        return False
    
    return True

def main():
    """Main launcher function."""
    print("Git Batch Pusher GUI Launcher")
    print("=" * 35)
    
    # Check dependencies
    if not check_dependencies():
        print("\nPlease install missing dependencies and try again.")
        input("Press Enter to exit...")
        return False
    
    print("✅ All dependencies are available")
    print("🚀 Launching GUI application...")
    
    try:
        # Import and run the GUI
        from git_batch_pusher_gui import main as gui_main
        gui_main()
        
    except ImportError as e:
        print(f"Error: Could not import GUI module: {e}")
        print("Make sure git_batch_pusher_gui.py is in the same directory.")
        input("Press Enter to exit...")
        return False
    
    except Exception as e:
        print(f"Error: Application failed to start: {e}")
        input("Press Enter to exit...")
        return False
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        sys.exit(0)
