@echo off
REM Git Batch Pusher GUI Launcher for Windows
REM This batch file provides an easy way to launch the GUI on Windows

title Git Batch Pusher GUI

echo Git Batch Pusher GUI Launcher
echo ================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    echo.
    pause
    exit /b 1
)

echo Python is available
echo.

REM Check if required files exist
if not exist "git_batch_pusher.py" (
    echo Error: git_batch_pusher.py not found
    echo Please make sure all files are in the same directory
    echo.
    pause
    exit /b 1
)

if not exist "git_batch_pusher_gui.py" (
    echo Error: git_batch_pusher_gui.py not found
    echo Please make sure all files are in the same directory
    echo.
    pause
    exit /b 1
)

echo All required files found
echo Launching GUI application...
echo.

REM Launch the GUI
python launch_gui.py

REM If we get here, the application has closed
echo.
echo Application closed.
pause
