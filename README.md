# Git Batch Pusher

A Python tool designed to push large projects to Git repositories in manageable chunks, helping overcome Git push size limitations.

## Features

- **Batch Processing**: Automatically groups files into configurable batch sizes (default 1GB)
- **Automated Git Operations**: Handles staging, committing, and pushing for each batch
- **Resume Functionality**: Can resume interrupted operations from where they left off
- **Flexible Configuration**: Support for both configuration files and command-line arguments
- **Progress Tracking**: Clear progress indicators and detailed logging
- **Error Handling**: Graceful error handling with informative messages
- **File Filtering**: Configurable exclude patterns to skip unwanted files

## Requirements

- Python 3.7 or higher
- Git installed and configured
- Target directory must be a Git repository

## Quick Start

### 1. Setup Your Git Repository

First, navigate to your target Git repository directory and ensure it's properly initialized:

```bash
cd /path/to/your/git/repo
git init
git remote add origin https://github.com/nam27062002/RO.git
```

### 2. Create Configuration File

Generate a sample configuration file:

```bash
python git_batch_pusher.py --create-config
```

This creates a `config.json` file that you can edit with your specific settings.

### 3. Edit Configuration

Edit the `config.json` file with your project details:

```json
{
  "source_path": "E:\\RO",
  "git_repo_url": "https://github.com/nam27062002/RO.git",
  "batch_size_mb": 1024,
  "commit_message_template": "Batch commit {batch_num}: {file_count} files ({size_mb:.1f}MB)",
  "exclude_patterns": [
    ".git", "__pycache__", "*.pyc", "*.pyo", 
    ".DS_Store", "Thumbs.db", "*.tmp", "*.log"
  ]
}
```

### 4. Run the Tool

```bash
# Using configuration file
python git_batch_pusher.py --config config.json

# Or using command-line arguments
python git_batch_pusher.py --source "E:\RO" --url "https://github.com/nam27062002/RO.git"
```

## Usage Examples

### Basic Usage with Config File
```bash
python git_batch_pusher.py --config config.json
```

### Command Line Arguments
```bash
python git_batch_pusher.py --source "E:\RO" --url "https://github.com/nam27062002/RO.git" --batch-size 512
```

### Dry Run (Preview Mode)
```bash
python git_batch_pusher.py --config config.json --dry-run
```

### Resume Interrupted Process
```bash
python git_batch_pusher.py --config config.json --resume
```

## Configuration Options

| Parameter | Description | Default |
|-----------|-------------|---------|
| `source_path` | Path to the source project directory | Required |
| `git_repo_url` | Git repository URL | Required |
| `batch_size_mb` | Maximum batch size in MB | 1024 (1GB) |
| `commit_message_template` | Template for commit messages | "Batch commit {batch_num}: {file_count} files ({size_mb:.1f}MB)" |
| `exclude_patterns` | List of file patterns to exclude | See config.json |
| `resume_file` | File to store progress information | ".git_batch_progress.json" |

## Command Line Options

```
usage: git_batch_pusher.py [-h] [--config CONFIG] [--source SOURCE] [--url URL] 
                          [--batch-size BATCH_SIZE] [--resume] [--create-config] 
                          [--dry-run]

options:
  -h, --help            show this help message and exit
  --config CONFIG, -c CONFIG
                        Path to configuration JSON file
  --source SOURCE, -s SOURCE
                        Source project directory path
  --url URL, -u URL     Git repository URL
  --batch-size BATCH_SIZE, -b BATCH_SIZE
                        Batch size in MB (default: 1024)
  --resume, -r          Resume from previous interrupted run
  --create-config       Create a sample configuration file
  --dry-run             Show what would be done without actually doing it
```

## How It Works

1. **Validation**: Checks that the source directory exists and the current directory is a Git repository
2. **File Scanning**: Recursively scans the source directory, excluding files matching the exclude patterns
3. **Batch Creation**: Groups files into batches based on the specified size limit
4. **Processing**: For each batch:
   - Copies files from source to the Git repository
   - Stages the files (`git add`)
   - Creates a commit with a descriptive message
   - Pushes the commit to the remote repository
5. **Progress Tracking**: Saves progress after each successful batch for resume functionality

## Error Handling and Recovery

- **Automatic Resume**: If the process is interrupted, use the `--resume` flag to continue from the last successful batch
- **Detailed Logging**: All operations are logged to both console and `git_batch_pusher.log`
- **Validation**: Pre-flight checks ensure the setup is correct before starting
- **Graceful Failures**: Individual batch failures don't stop the entire process

## Troubleshooting

### Common Issues

1. **"Current directory is not a Git repository"**
   - Ensure you're running the script from within a Git repository
   - Initialize with `git init` if needed

2. **"Source path does not exist"**
   - Verify the source path in your configuration is correct
   - Use absolute paths to avoid confusion

3. **Git push failures**
   - Check your Git credentials and remote repository access
   - Ensure the remote repository exists and you have push permissions

4. **Large file warnings**
   - Consider reducing batch size for repositories with file size limits
   - Use Git LFS for very large files if needed

## License

This tool is provided as-is for project migration assistance. Feel free to modify and distribute as needed.
