# Git Batch Pusher GUI

Công cụ GUI để đẩy các dự án lớn lên Git repository theo từng batch.

## Cách sử dụng

### Windows
Double-click `run_gui.bat`

### <PERSON><PERSON><PERSON><PERSON>
```bash
python run_gui.py
```

## Tính năng

- **Tự động setup Git repository**: Không cần chuẩn bị gì trước
- **Giao diện đơn giản**: Chỉ cần nhập 2 thông tin chính:
  - Đường dẫn project source
  - URL Git repository
- **Xử lý tự động**: Tool sẽ tự động:
  - Khởi tạo Git repository nếu chưa có
  - Cấu hình remote origin
  - Chia files thành các batch
  - Commit và push từng batch
- **Resume**: <PERSON><PERSON> th<PERSON> tiếp tục nếu bị gián đoạn
- **Progress tracking**: <PERSON> dõ<PERSON> tiến trình real-time

## Y<PERSON><PERSON> cầu

- Python 3.7+
- Git đã cài đặt
- Kết nối internet

Chỉ cần cung cấp đường dẫn project và Git URL, tool sẽ tự xử lý tất cả!
