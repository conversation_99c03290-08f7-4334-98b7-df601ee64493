#!/usr/bin/env python3
"""
Git Batch Pusher - A tool to push large projects to Git repositories in manageable chunks.

This tool helps overcome Git push size limitations by:
1. Grouping files into configurable batch sizes (default 1GB)
2. Automatically staging, committing, and pushing each batch
3. Providing progress indicators and error handling
4. Supporting resume functionality if interrupted

Author: Generated for project migration assistance
"""

import os
import sys
import json
import argparse
import subprocess
import logging
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import hashlib


@dataclass
class Config:
    """Configuration class for the Git Batch Pusher."""
    source_path: str
    git_repo_url: str
    batch_size_mb: int = 1024  # Default 1GB in MB
    commit_message_template: str = "Batch commit {batch_num}: {file_count} files ({size_mb:.1f}MB)"
    exclude_patterns: List[str] = None
    resume_file: str = ".git_batch_progress.json"
    
    def __post_init__(self):
        if self.exclude_patterns is None:
            self.exclude_patterns = [
                '.git', '__pycache__', '*.pyc', '*.pyo', 
                '.DS_Store', 'Thumbs.db', '*.tmp', '*.log'
            ]


class GitBatchPusher:
    """Main class for batch pushing files to Git repository."""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = self._setup_logging()
        self.processed_files = set()
        self.current_batch = 1
        self.total_files = 0
        self.total_size = 0
        
    def _setup_logging(self) -> logging.Logger:
        """Set up logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('git_batch_pusher.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        return logging.getLogger(__name__)
    
    def validate_setup(self) -> bool:
        """Validate that the setup is correct before starting."""
        # Check if source path exists
        if not os.path.exists(self.config.source_path):
            self.logger.error(f"Source path does not exist: {self.config.source_path}")
            return False
        
        # Check if current directory is a Git repository
        if not os.path.exists('.git'):
            self.logger.error("Current directory is not a Git repository")
            return False
        
        # Check Git status
        try:
            result = subprocess.run(['git', 'status'], capture_output=True, text=True, check=True)
            self.logger.info("Git repository status validated")
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Git status check failed: {e}")
            return False
        
        return True
    
    def get_file_size(self, filepath: str) -> int:
        """Get file size in bytes."""
        try:
            return os.path.getsize(filepath)
        except OSError:
            return 0
    
    def should_exclude_file(self, filepath: str) -> bool:
        """Check if file should be excluded based on patterns."""
        import fnmatch
        
        for pattern in self.config.exclude_patterns:
            if fnmatch.fnmatch(os.path.basename(filepath), pattern) or \
               fnmatch.fnmatch(filepath, pattern):
                return True
        return False
    
    def scan_files(self) -> List[Tuple[str, int]]:
        """Scan source directory and return list of (filepath, size) tuples."""
        files = []
        source_path = Path(self.config.source_path)
        
        self.logger.info(f"Scanning files in: {self.config.source_path}")
        
        for root, dirs, filenames in os.walk(source_path):
            # Remove excluded directories from dirs list to avoid traversing them
            dirs[:] = [d for d in dirs if not self.should_exclude_file(d)]
            
            for filename in filenames:
                filepath = os.path.join(root, filename)
                
                if self.should_exclude_file(filepath):
                    continue
                
                size = self.get_file_size(filepath)
                if size > 0:  # Only include non-empty files
                    # Make path relative to source
                    rel_path = os.path.relpath(filepath, self.config.source_path)
                    files.append((rel_path, size))
        
        self.total_files = len(files)
        self.total_size = sum(size for _, size in files)
        
        self.logger.info(f"Found {self.total_files} files, total size: {self.total_size / (1024*1024):.1f}MB")
        return files
    
    def create_batches(self, files: List[Tuple[str, int]]) -> List[List[Tuple[str, int]]]:
        """Group files into batches based on size limit."""
        batches = []
        current_batch = []
        current_size = 0
        batch_size_bytes = self.config.batch_size_mb * 1024 * 1024
        
        for filepath, size in files:
            # If adding this file would exceed batch size and current batch is not empty
            if current_size + size > batch_size_bytes and current_batch:
                batches.append(current_batch)
                current_batch = []
                current_size = 0
            
            current_batch.append((filepath, size))
            current_size += size
        
        # Add the last batch if it's not empty
        if current_batch:
            batches.append(current_batch)
        
        self.logger.info(f"Created {len(batches)} batches")
        return batches
    
    def copy_files_to_repo(self, batch: List[Tuple[str, int]]) -> bool:
        """Copy files from source to current directory (Git repo)."""
        try:
            for rel_path, _ in batch:
                source_file = os.path.join(self.config.source_path, rel_path)
                dest_file = rel_path
                
                # Create destination directory if it doesn't exist
                dest_dir = os.path.dirname(dest_file)
                if dest_dir:
                    os.makedirs(dest_dir, exist_ok=True)
                
                # Copy file
                import shutil
                shutil.copy2(source_file, dest_file)
                
            return True
        except Exception as e:
            self.logger.error(f"Error copying files: {e}")
            return False
    
    def git_add_commit_push(self, batch: List[Tuple[str, int]], batch_num: int) -> bool:
        """Stage, commit, and push the current batch."""
        try:
            # Stage files
            file_paths = [filepath for filepath, _ in batch]
            subprocess.run(['git', 'add'] + file_paths, check=True)
            
            # Create commit message
            total_size_mb = sum(size for _, size in batch) / (1024 * 1024)
            commit_msg = self.config.commit_message_template.format(
                batch_num=batch_num,
                file_count=len(batch),
                size_mb=total_size_mb
            )
            
            # Commit
            subprocess.run(['git', 'commit', '-m', commit_msg], check=True)
            
            # Push
            subprocess.run(['git', 'push', 'origin', 'main'], check=True)
            
            self.logger.info(f"Successfully pushed batch {batch_num}")
            return True
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Git operation failed for batch {batch_num}: {e}")
            return False

    def save_progress(self, batch_num: int, processed_files: List[str]):
        """Save current progress to resume file."""
        progress_data = {
            'batch_num': batch_num,
            'processed_files': processed_files,
            'timestamp': datetime.now().isoformat()
        }

        try:
            with open(self.config.resume_file, 'w') as f:
                json.dump(progress_data, f, indent=2)
        except Exception as e:
            self.logger.warning(f"Could not save progress: {e}")

    def load_progress(self) -> Optional[Dict]:
        """Load progress from resume file."""
        if not os.path.exists(self.config.resume_file):
            return None

        try:
            with open(self.config.resume_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            self.logger.warning(f"Could not load progress: {e}")
            return None

    def process_batches(self, resume: bool = False) -> bool:
        """Main method to process all batches."""
        files = self.scan_files()
        if not files:
            self.logger.warning("No files found to process")
            return True

        batches = self.create_batches(files)
        start_batch = 1
        processed_files = []

        # Handle resume functionality
        if resume:
            progress = self.load_progress()
            if progress:
                start_batch = progress['batch_num']
                processed_files = progress['processed_files']
                self.logger.info(f"Resuming from batch {start_batch}")

        # Process each batch
        for i, batch in enumerate(batches[start_batch-1:], start=start_batch):
            batch_size_mb = sum(size for _, size in batch) / (1024 * 1024)
            self.logger.info(f"Processing batch {i}/{len(batches)} - {len(batch)} files ({batch_size_mb:.1f}MB)")

            # Show files being processed
            self.logger.info("Files in this batch:")
            for filepath, size in batch[:5]:  # Show first 5 files
                self.logger.info(f"  - {filepath} ({size / 1024:.1f}KB)")
            if len(batch) > 5:
                self.logger.info(f"  ... and {len(batch) - 5} more files")

            # Copy files to repository
            if not self.copy_files_to_repo(batch):
                self.logger.error(f"Failed to copy files for batch {i}")
                return False

            # Git operations
            if not self.git_add_commit_push(batch, i):
                self.logger.error(f"Failed to push batch {i}")
                return False

            # Update progress
            batch_files = [filepath for filepath, _ in batch]
            processed_files.extend(batch_files)
            self.save_progress(i + 1, processed_files)

            self.logger.info(f"Batch {i} completed successfully")

        # Clean up progress file on successful completion
        if os.path.exists(self.config.resume_file):
            os.remove(self.config.resume_file)

        self.logger.info("All batches processed successfully!")
        return True


def load_config_from_file(config_path: str) -> Optional[Config]:
    """Load configuration from JSON file."""
    if not os.path.exists(config_path):
        return None

    try:
        with open(config_path, 'r') as f:
            config_data = json.load(f)
        return Config(**config_data)
    except Exception as e:
        print(f"Error loading config file: {e}")
        return None


def create_sample_config(config_path: str):
    """Create a sample configuration file."""
    sample_config = Config(
        source_path="E:\\RO",
        git_repo_url="https://github.com/nam27062002/RO.git",
        batch_size_mb=1024,
        commit_message_template="Batch commit {batch_num}: {file_count} files ({size_mb:.1f}MB)"
    )

    try:
        with open(config_path, 'w') as f:
            json.dump(asdict(sample_config), f, indent=2)
        print(f"Sample configuration created at: {config_path}")
        print("Please edit the configuration file with your specific settings.")
    except Exception as e:
        print(f"Error creating sample config: {e}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Git Batch Pusher - Push large projects in manageable chunks",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --config config.json                    # Use configuration file
  %(prog)s --source E:\\RO --url https://github.com/user/repo.git  # Command line args
  %(prog)s --create-config                         # Create sample config file
  %(prog)s --config config.json --resume           # Resume interrupted process
        """
    )

    parser.add_argument('--config', '-c', help='Path to configuration JSON file')
    parser.add_argument('--source', '-s', help='Source project directory path')
    parser.add_argument('--url', '-u', help='Git repository URL')
    parser.add_argument('--batch-size', '-b', type=int, default=1024,
                       help='Batch size in MB (default: 1024)')
    parser.add_argument('--resume', '-r', action='store_true',
                       help='Resume from previous interrupted run')
    parser.add_argument('--create-config', action='store_true',
                       help='Create a sample configuration file')
    parser.add_argument('--dry-run', action='store_true',
                       help='Show what would be done without actually doing it')

    args = parser.parse_args()

    # Handle config file creation
    if args.create_config:
        create_sample_config('config.json')
        return

    # Load configuration
    config = None
    if args.config:
        config = load_config_from_file(args.config)
        if not config:
            print(f"Failed to load configuration from: {args.config}")
            return
    elif args.source and args.url:
        config = Config(
            source_path=args.source,
            git_repo_url=args.url,
            batch_size_mb=args.batch_size
        )
    else:
        print("Error: Either provide --config file or both --source and --url")
        parser.print_help()
        return

    # Initialize and run the pusher
    pusher = GitBatchPusher(config)

    if not pusher.validate_setup():
        print("Setup validation failed. Please check the error messages above.")
        return

    if args.dry_run:
        print("DRY RUN MODE - No actual Git operations will be performed")
        files = pusher.scan_files()
        batches = pusher.create_batches(files)
        print(f"Would create {len(batches)} batches")
        for i, batch in enumerate(batches, 1):
            size_mb = sum(size for _, size in batch) / (1024 * 1024)
            print(f"Batch {i}: {len(batch)} files ({size_mb:.1f}MB)")
        return

    success = pusher.process_batches(resume=args.resume)
    if success:
        print("Project successfully pushed to Git repository!")
    else:
        print("Process failed. Check the logs for details.")
        print("You can resume with --resume flag.")


if __name__ == "__main__":
    main()
