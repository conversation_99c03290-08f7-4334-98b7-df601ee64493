#!/usr/bin/env python3
"""
Setup script for Git Batch Pusher

This script helps users set up their Git repository and configuration
for using the Git Batch Pusher tool.
"""

import os
import sys
import subprocess
import json
from pathlib import Path


def check_git_installed():
    """Check if Git is installed and accessible."""
    try:
        result = subprocess.run(['git', '--version'], capture_output=True, text=True)
        print(f"✓ Git is installed: {result.stdout.strip()}")
        return True
    except FileNotFoundError:
        print("✗ Git is not installed or not in PATH")
        print("Please install Git from https://git-scm.com/")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info >= (3, 7):
        print(f"✓ Python version is compatible: {sys.version}")
        return True
    else:
        print(f"✗ Python version {sys.version} is not compatible")
        print("Please upgrade to Python 3.7 or higher")
        return False


def setup_git_repository(repo_url: str, target_dir: str = None):
    """Set up Git repository."""
    if target_dir is None:
        target_dir = input("Enter target directory for Git repository (or press Enter for current directory): ").strip()
        if not target_dir:
            target_dir = "."
    
    target_path = Path(target_dir).resolve()
    
    # Create directory if it doesn't exist
    target_path.mkdir(parents=True, exist_ok=True)
    
    # Change to target directory
    os.chdir(target_path)
    
    # Check if already a Git repository
    if os.path.exists('.git'):
        print(f"✓ Directory {target_path} is already a Git repository")
    else:
        print(f"Initializing Git repository in {target_path}")
        subprocess.run(['git', 'init'], check=True)
        print("✓ Git repository initialized")
    
    # Add remote origin if provided
    if repo_url:
        try:
            # Check if origin already exists
            result = subprocess.run(['git', 'remote', 'get-url', 'origin'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                current_url = result.stdout.strip()
                if current_url != repo_url:
                    print(f"Remote origin exists with different URL: {current_url}")
                    update = input(f"Update to {repo_url}? (y/n): ").lower().startswith('y')
                    if update:
                        subprocess.run(['git', 'remote', 'set-url', 'origin', repo_url], check=True)
                        print("✓ Remote origin updated")
                else:
                    print("✓ Remote origin already configured correctly")
            else:
                subprocess.run(['git', 'remote', 'add', 'origin', repo_url], check=True)
                print("✓ Remote origin added")
        except subprocess.CalledProcessError as e:
            print(f"Warning: Could not configure remote origin: {e}")
    
    return str(target_path)


def create_interactive_config():
    """Create configuration file interactively."""
    print("\nCreating configuration file...")
    print("Please provide the following information:")
    
    # Get source path
    while True:
        source_path = input("Source project path (e.g., E:\\RO): ").strip()
        if source_path and os.path.exists(source_path):
            break
        elif source_path:
            print(f"Path '{source_path}' does not exist. Please try again.")
        else:
            print("Source path is required.")
    
    # Get Git repository URL
    while True:
        git_url = input("Git repository URL (e.g., https://github.com/user/repo.git): ").strip()
        if git_url:
            break
        print("Git repository URL is required.")
    
    # Get batch size
    while True:
        try:
            batch_size = input("Batch size in MB (default: 1024): ").strip()
            if not batch_size:
                batch_size = 1024
            else:
                batch_size = int(batch_size)
            break
        except ValueError:
            print("Please enter a valid number.")
    
    # Create configuration
    config = {
        "source_path": source_path,
        "git_repo_url": git_url,
        "batch_size_mb": batch_size,
        "commit_message_template": "Batch commit {batch_num}: {file_count} files ({size_mb:.1f}MB)",
        "exclude_patterns": [
            ".git", "__pycache__", "*.pyc", "*.pyo", 
            ".DS_Store", "Thumbs.db", "*.tmp", "*.log",
            "node_modules", ".vscode", ".idea", "*.exe", "*.dll"
        ],
        "resume_file": ".git_batch_progress.json"
    }
    
    # Save configuration
    config_file = "config.json"
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"✓ Configuration saved to {config_file}")
    return config


def main():
    """Main setup function."""
    print("Git Batch Pusher Setup")
    print("=" * 30)
    
    # Check prerequisites
    if not check_python_version():
        return False
    
    if not check_git_installed():
        return False
    
    print("\nPrerequisites check passed!")
    
    # Interactive setup
    print("\nSetup Options:")
    print("1. Full setup (Git repository + configuration)")
    print("2. Configuration only")
    print("3. Git repository only")
    
    while True:
        choice = input("\nSelect option (1-3): ").strip()
        if choice in ['1', '2', '3']:
            break
        print("Please enter 1, 2, or 3")
    
    git_url = None
    target_dir = None
    
    if choice in ['1', '3']:
        # Git repository setup
        git_url = input("Git repository URL: ").strip()
        if choice == '1' or input("Setup in current directory? (y/n): ").lower().startswith('n'):
            target_dir = input("Target directory (press Enter for current): ").strip() or "."
        
        repo_path = setup_git_repository(git_url, target_dir)
        print(f"✓ Git repository setup complete in {repo_path}")
    
    if choice in ['1', '2']:
        # Configuration setup
        config = create_interactive_config()
        print("✓ Configuration setup complete")
    
    print("\n" + "=" * 50)
    print("Setup complete! You can now run:")
    print("  python git_batch_pusher.py --config config.json")
    print("\nFor help, run:")
    print("  python git_batch_pusher.py --help")
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\nSetup cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nSetup failed: {e}")
        sys.exit(1)
