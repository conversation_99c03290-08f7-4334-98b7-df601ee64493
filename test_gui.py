#!/usr/bin/env python3
"""
Test script for the Git Batch Pusher GUI

This script tests the GUI functionality without actually performing Git operations.
"""

import tkinter as tk
from tkinter import messagebox
import tempfile
import os
import sys

def test_gui_import():
    """Test if the GUI can be imported successfully."""
    try:
        from git_batch_pusher_gui import GitBatchPusherGUI, ToolTip
        print("✅ GUI module imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import GUI module: {e}")
        return False

def test_gui_creation():
    """Test if the GUI can be created without errors."""
    try:
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        from git_batch_pusher_gui import GitBatchPusherGUI
        app = GitBatchPusherGUI(root)
        
        print("✅ GUI created successfully")
        
        # Test basic functionality
        app.source_path_var.set("C:\\test\\path")
        app.git_url_var.set("https://github.com/test/repo.git")
        app.batch_size_var.set("512")
        
        config = app.create_config_from_gui()
        assert config.source_path == "C:\\test\\path"
        assert config.git_repo_url == "https://github.com/test/repo.git"
        assert config.batch_size_mb == 512
        
        print("✅ Configuration creation works")
        
        # Test exclude patterns
        app.exclude_text.delete("1.0", "end")
        app.exclude_text.insert("1.0", "*.pyc\n*.tmp\n.git")
        patterns = app.get_exclude_patterns()
        assert "*.pyc" in patterns
        assert "*.tmp" in patterns
        assert ".git" in patterns
        
        print("✅ Exclude patterns work")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI creation failed: {e}")
        return False

def test_tooltip():
    """Test tooltip functionality."""
    try:
        root = tk.Tk()
        root.withdraw()
        
        from git_batch_pusher_gui import ToolTip
        
        # Create a test widget
        label = tk.Label(root, text="Test")
        tooltip = ToolTip(label, "Test tooltip")
        
        print("✅ Tooltip created successfully")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Tooltip test failed: {e}")
        return False

def test_config_save_load():
    """Test configuration save and load functionality."""
    try:
        root = tk.Tk()
        root.withdraw()
        
        from git_batch_pusher_gui import GitBatchPusherGUI
        app = GitBatchPusherGUI(root)
        
        # Set test configuration
        app.source_path_var.set("E:\\RO")
        app.git_url_var.set("https://github.com/nam27062002/RO.git")
        app.batch_size_var.set("1024")
        
        # Test save last config
        app.save_last_config()
        
        # Reset values
        app.source_path_var.set("")
        app.git_url_var.set("")
        app.batch_size_var.set("512")
        
        # Test load last config
        app.load_last_config()
        
        # Check if values were restored
        assert app.source_path_var.get() == "E:\\RO"
        assert app.git_url_var.get() == "https://github.com/nam27062002/RO.git"
        assert app.batch_size_var.get() == "1024"
        
        print("✅ Configuration save/load works")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Config save/load test failed: {e}")
        return False

def run_gui_tests():
    """Run all GUI tests."""
    print("Git Batch Pusher GUI Tests")
    print("=" * 30)
    
    tests = [
        ("Import Test", test_gui_import),
        ("GUI Creation Test", test_gui_creation),
        ("Tooltip Test", test_tooltip),
        ("Config Save/Load Test", test_config_save_load),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\nRunning {test_name}...")
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
    
    print(f"\n" + "=" * 30)
    print(f"Tests completed: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! GUI is ready to use.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        return False

def main():
    """Main test function."""
    try:
        success = run_gui_tests()
        
        if success:
            print("\n🚀 You can now run the GUI with:")
            print("   python launch_gui.py")
            print("   or double-click launch_gui.bat (Windows)")
        
        return success
        
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        return False
    except Exception as e:
        print(f"\nTest suite failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
