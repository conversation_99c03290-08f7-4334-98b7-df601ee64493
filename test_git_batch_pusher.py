#!/usr/bin/env python3
"""
Test script for Git Batch Pusher

This script creates a test environment to validate the Git Batch Pusher functionality.
"""

import os
import tempfile
import shutil
import subprocess
import json
from pathlib import Path
from git_batch_pusher import Config, GitBatchPusher


def create_test_files(test_dir: str, num_files: int = 10, file_size_kb: int = 100):
    """Create test files of specified size."""
    os.makedirs(test_dir, exist_ok=True)
    
    for i in range(num_files):
        file_path = os.path.join(test_dir, f"test_file_{i:03d}.txt")
        
        # Create file with specified size
        content = f"Test file {i}\n" + "x" * (file_size_kb * 1024 - 20)
        
        with open(file_path, 'w') as f:
            f.write(content)
    
    # Create some subdirectories with files
    subdir = os.path.join(test_dir, "subdir")
    os.makedirs(subdir, exist_ok=True)
    
    for i in range(5):
        file_path = os.path.join(subdir, f"sub_file_{i}.txt")
        with open(file_path, 'w') as f:
            f.write(f"Subdirectory file {i}\n" + "y" * (file_size_kb * 512))


def test_file_scanning():
    """Test the file scanning functionality."""
    print("Testing file scanning...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_source = os.path.join(temp_dir, "source")
        create_test_files(test_source, num_files=5, file_size_kb=50)
        
        config = Config(
            source_path=test_source,
            git_repo_url="https://github.com/test/test.git",
            batch_size_mb=1  # Small batch size for testing
        )
        
        pusher = GitBatchPusher(config)
        files = pusher.scan_files()
        
        print(f"Found {len(files)} files")
        for filepath, size in files[:3]:
            print(f"  {filepath}: {size} bytes")
        
        assert len(files) > 0, "Should find some files"
        print("✓ File scanning test passed")


def test_batch_creation():
    """Test the batch creation functionality."""
    print("Testing batch creation...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_source = os.path.join(temp_dir, "source")
        create_test_files(test_source, num_files=10, file_size_kb=200)  # 200KB files
        
        config = Config(
            source_path=test_source,
            git_repo_url="https://github.com/test/test.git",
            batch_size_mb=1  # 1MB batch size
        )
        
        pusher = GitBatchPusher(config)
        files = pusher.scan_files()
        batches = pusher.create_batches(files)
        
        print(f"Created {len(batches)} batches from {len(files)} files")
        
        for i, batch in enumerate(batches):
            batch_size = sum(size for _, size in batch)
            print(f"  Batch {i+1}: {len(batch)} files, {batch_size / 1024:.1f}KB")
        
        assert len(batches) > 1, "Should create multiple batches"
        print("✓ Batch creation test passed")


def test_config_loading():
    """Test configuration loading from file."""
    print("Testing configuration loading...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_file = os.path.join(temp_dir, "test_config.json")
        
        test_config = {
            "source_path": "/test/path",
            "git_repo_url": "https://github.com/test/repo.git",
            "batch_size_mb": 512,
            "commit_message_template": "Test commit {batch_num}"
        }
        
        with open(config_file, 'w') as f:
            json.dump(test_config, f)
        
        from git_batch_pusher import load_config_from_file
        loaded_config = load_config_from_file(config_file)
        
        assert loaded_config is not None, "Should load config successfully"
        assert loaded_config.source_path == "/test/path", "Should load correct source path"
        assert loaded_config.batch_size_mb == 512, "Should load correct batch size"
        
        print("✓ Configuration loading test passed")


def test_exclude_patterns():
    """Test file exclusion patterns."""
    print("Testing exclude patterns...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_source = os.path.join(temp_dir, "source")
        os.makedirs(test_source, exist_ok=True)
        
        # Create files that should be included
        with open(os.path.join(test_source, "include.txt"), 'w') as f:
            f.write("Include this file")
        
        with open(os.path.join(test_source, "data.json"), 'w') as f:
            f.write('{"test": true}')
        
        # Create files that should be excluded
        with open(os.path.join(test_source, "exclude.pyc"), 'w') as f:
            f.write("Exclude this file")
        
        with open(os.path.join(test_source, "temp.tmp"), 'w') as f:
            f.write("Temporary file")
        
        config = Config(
            source_path=test_source,
            git_repo_url="https://github.com/test/test.git"
        )
        
        pusher = GitBatchPusher(config)
        files = pusher.scan_files()
        
        included_files = [filepath for filepath, _ in files]
        
        assert "include.txt" in included_files, "Should include .txt files"
        assert "data.json" in included_files, "Should include .json files"
        assert not any("exclude.pyc" in f for f in included_files), "Should exclude .pyc files"
        assert not any("temp.tmp" in f for f in included_files), "Should exclude .tmp files"
        
        print(f"Included files: {included_files}")
        print("✓ Exclude patterns test passed")


def run_all_tests():
    """Run all tests."""
    print("Running Git Batch Pusher Tests")
    print("=" * 40)
    
    try:
        test_config_loading()
        test_file_scanning()
        test_batch_creation()
        test_exclude_patterns()
        
        print("\n" + "=" * 40)
        print("✓ All tests passed!")
        
    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        raise


if __name__ == "__main__":
    run_all_tests()
