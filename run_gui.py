#!/usr/bin/env python3
"""
Simple launcher for Git Batch Pusher GUI
"""

import sys
import os

def main():
    """Launch the GUI application."""
    try:
        # Check Python version
        if sys.version_info < (3, 7):
            print(f"Error: Python 3.7+ required. Current: {sys.version}")
            input("Press Enter to exit...")
            return
        
        # Check tkinter
        try:
            import tkinter
        except ImportError:
            print("Error: tkinter not available. Please install tkinter.")
            input("Press Enter to exit...")
            return
        
        # Launch GUI
        from git_batch_pusher_gui import main as gui_main
        gui_main()
        
    except ImportError as e:
        print(f"Error: Missing module - {e}")
        print("Make sure all files are in the same directory.")
        input("Press Enter to exit...")
    except Exception as e:
        print(f"Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
